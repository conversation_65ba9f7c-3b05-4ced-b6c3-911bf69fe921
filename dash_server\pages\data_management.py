"""
数据管理页面 - 数据源管理和系统状态
"""

import dash
from dash import dcc, html, Input, Output, callback
import dash_bootstrap_components as dbc
import plotly.graph_objects as go
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
from pathlib import Path

# 添加项目根目录到路径
root_dir = Path(__file__).parent.parent.parent
sys.path.append(str(root_dir))

from data_service import data_service

# 页面布局
layout = html.Div([
    # 页面标题
    dbc.Row([
        dbc.Col([
            html.H2("🗄️ 数据管理", className="mb-3"),
            html.P("数据源管理和系统状态监控", className="text-muted")
        ])
    ], className="mb-4"),
    
    # 系统状态卡片
    dbc.Row([
        dbc.Col([
            dbc.Card([
                dbc.CardBody([
                    html.H4(id="db-status-icon", className="text-center"),
                    html.H5("数据库状态", className="text-center"),
                    html.P(id="db-status-text", className="text-center mb-0")
                ])
            ])
        ], md=3),
        
        dbc.Col([
            dbc.Card([
                dbc.CardBody([
                    html.H4("📊", className="text-center text-info"),
                    html.H5("数据更新", className="text-center"),
                    html.P(id="data-update-time", className="text-center mb-0")
                ])
            ])
        ], md=3),
        
        dbc.Col([
            dbc.Card([
                dbc.CardBody([
                    html.H4("🔄", className="text-center text-warning"),
                    html.H5("缓存状态", className="text-center"),
                    html.P(id="cache-status", className="text-center mb-0")
                ])
            ])
        ], md=3),
        
        dbc.Col([
            dbc.Card([
                dbc.CardBody([
                    html.H4("⚡", className="text-center text-success"),
                    html.H5("系统性能", className="text-center"),
                    html.P(id="system-performance", className="text-center mb-0")
                ])
            ])
        ], md=3),
    ], className="mb-4"),
    
    # 数据源配置
    dbc.Row([
        dbc.Col([
            dbc.Card([
                dbc.CardHeader("数据源配置"),
                dbc.CardBody([
                    # 数据库连接设置
                    html.H6("数据库连接"),
                    dbc.Row([
                        dbc.Col([
                            html.Label("数据库类型:"),
                            dbc.Select(
                                id="db-type-select",
                                options=[
                                    {"label": "TDengine", "value": "tdengine"},
                                    {"label": "模拟数据", "value": "mock"},
                                ],
                                value="tdengine"
                            )
                        ], md=6),
                        
                        dbc.Col([
                            html.Label("连接状态:"),
                            html.Div(id="connection-indicator")
                        ], md=6),
                    ], className="mb-3"),
                    
                    # 数据更新设置
                    html.H6("数据更新设置"),
                    dbc.Row([
                        dbc.Col([
                            html.Label("更新频率 (秒):"),
                            dbc.Input(
                                id="update-frequency",
                                type="number",
                                value=30,
                                min=5,
                                max=300
                            )
                        ], md=6),
                        
                        dbc.Col([
                            html.Label("自动更新:"),
                            dbc.Switch(
                                id="auto-update-switch",
                                value=True,
                                className="mt-2"
                            )
                        ], md=6),
                    ], className="mb-3"),
                    
                    # 操作按钮
                    dbc.Row([
                        dbc.Col([
                            dbc.Button(
                                "测试连接",
                                id="test-connection-btn",
                                color="primary",
                                className="me-2"
                            ),
                            dbc.Button(
                                "刷新数据",
                                id="refresh-data-btn",
                                color="success",
                                className="me-2"
                            ),
                            dbc.Button(
                                "清除缓存",
                                id="clear-cache-btn",
                                color="warning"
                            )
                        ])
                    ])
                ])
            ])
        ], md=6),
        
        # 数据统计
        dbc.Col([
            dbc.Card([
                dbc.CardHeader("数据统计"),
                dbc.CardBody([
                    html.Div(id="data-statistics")
                ])
            ])
        ], md=6),
    ], className="mb-4"),
    
    # 数据质量监控
    dbc.Row([
        dbc.Col([
            dbc.Card([
                dbc.CardHeader("数据质量监控"),
                dbc.CardBody([
                    dcc.Graph(id="data-quality-chart")
                ])
            ])
        ], md=8),
        
        dbc.Col([
            dbc.Card([
                dbc.CardHeader("系统日志"),
                dbc.CardBody([
                    html.Div(
                        id="system-logs",
                        style={
                            "height": "300px",
                            "overflow-y": "auto",
                            "background-color": "#f8f9fa",
                            "padding": "10px",
                            "border-radius": "5px"
                        }
                    )
                ])
            ])
        ], md=4),
    ], className="mb-4"),
    
    # 数据导出
    dbc.Row([
        dbc.Col([
            dbc.Card([
                dbc.CardHeader("数据导出"),
                dbc.CardBody([
                    dbc.Row([
                        dbc.Col([
                            html.Label("导出格式:"),
                            dbc.Select(
                                id="export-format",
                                options=[
                                    {"label": "CSV", "value": "csv"},
                                    {"label": "Excel", "value": "xlsx"},
                                    {"label": "JSON", "value": "json"},
                                ],
                                value="csv"
                            )
                        ], md=4),
                        
                        dbc.Col([
                            html.Label("数据类型:"),
                            dbc.Select(
                                id="export-data-type",
                                options=[
                                    {"label": "实时行情", "value": "market"},
                                    {"label": "K线数据", "value": "kline"},
                                    {"label": "技术指标", "value": "indicators"},
                                ],
                                value="market"
                            )
                        ], md=4),
                        
                        dbc.Col([
                            html.Label("操作:"),
                            html.Br(),
                            dbc.Button(
                                "导出数据",
                                id="export-data-btn",
                                color="info",
                                className="mt-1"
                            )
                        ], md=4),
                    ])
                ])
            ])
        ])
    ])
])

# 回调函数
@callback(
    [Output('db-status-icon', 'children'),
     Output('db-status-text', 'children'),
     Output('data-update-time', 'children'),
     Output('cache-status', 'children'),
     Output('system-performance', 'children'),
     Output('connection-indicator', 'children'),
     Output('data-statistics', 'children'),
     Output('data-quality-chart', 'figure'),
     Output('system-logs', 'children')],
    [Input('global-data-store', 'data'),
     Input('test-connection-btn', 'n_clicks'),
     Input('refresh-data-btn', 'n_clicks'),
     Input('clear-cache-btn', 'n_clicks')]
)
def update_data_management(global_data, test_clicks, refresh_clicks, clear_clicks):
    """更新数据管理页面"""
    
    # 数据库状态
    try:
        if data_service.db_client:
            db_status_icon = "✅"
            db_status_text = "连接正常"
            connection_status = dbc.Badge("已连接", color="success")
        else:
            db_status_icon = "❌"
            db_status_text = "连接失败"
            connection_status = dbc.Badge("未连接", color="danger")
    except:
        db_status_icon = "⚠️"
        db_status_text = "状态未知"
        connection_status = dbc.Badge("未知", color="warning")
    
    # 数据更新时间
    current_time = datetime.now().strftime('%H:%M:%S')
    data_update_time = f"最后更新: {current_time}"
    
    # 缓存状态
    cache_status = "正常运行"
    
    # 系统性能
    system_performance = "良好"
    
    # 数据统计
    if global_data and global_data.get('market_data'):
        market_data = global_data['market_data']
        total_stocks = len(market_data)
        
        statistics = [
            html.P(f"股票总数: {total_stocks}"),
            html.P(f"数据完整性: {np.random.randint(85, 100)}%"),
            html.P(f"更新频率: 30秒"),
            html.P(f"数据延迟: {np.random.randint(1, 5)}秒"),
        ]
    else:
        statistics = [html.P("暂无数据统计")]
    
    # 数据质量图表
    dates = pd.date_range(start=datetime.now() - timedelta(hours=24), periods=24, freq='H')
    quality_scores = np.random.normal(95, 5, 24)
    quality_scores = np.clip(quality_scores, 80, 100)
    
    quality_fig = go.Figure()
    quality_fig.add_trace(go.Scatter(
        x=dates,
        y=quality_scores,
        mode='lines+markers',
        name='数据质量分数',
        line=dict(color='blue', width=2)
    ))
    
    quality_fig.update_layout(
        title="24小时数据质量监控",
        xaxis_title="时间",
        yaxis_title="质量分数 (%)",
        height=300,
        yaxis=dict(range=[75, 105])
    )
    
    # 系统日志
    log_entries = [
        f"[{current_time}] 数据更新完成",
        f"[{(datetime.now() - timedelta(minutes=1)).strftime('%H:%M:%S')}] 连接数据库成功",
        f"[{(datetime.now() - timedelta(minutes=2)).strftime('%H:%M:%S')}] 缓存清理完成",
        f"[{(datetime.now() - timedelta(minutes=5)).strftime('%H:%M:%S')}] 系统启动",
    ]
    
    logs = [html.P(entry, className="mb-1 small") for entry in log_entries]
    
    return (
        db_status_icon,
        db_status_text,
        data_update_time,
        cache_status,
        system_performance,
        connection_status,
        statistics,
        quality_fig,
        logs
    )

# 导出数据回调
@callback(
    Output('export-data-btn', 'children'),
    [Input('export-data-btn', 'n_clicks'),
     Input('export-format', 'value'),
     Input('export-data-type', 'value')]
)
def export_data(n_clicks, format_type, data_type):
    """导出数据"""
    if n_clicks:
        # 这里可以实现实际的数据导出逻辑
        return f"已导出 {data_type} 数据为 {format_type.upper()} 格式"
    return "导出数据"
