import time
from queue import Empty, Queue
from xtquant import xtdata
from utils.object import TickData, BarData, Interval
from utils.utility import BarGenerator, generate_datetime
from utils.constant import Exchange, Interval, TickDirection

class  QmtServer:
    def __init__(self):
        self._active = False
        self.full_tick = {}
        self.full_minute = {}
        self.sub_sleep = 60
        self.queue_list: list[Queue] = []
        # self.add_queue(Interval.TICK.value, Queue())

    def add_queue(self, queue: Queue):
        self.queue_list.append(queue)

    def get_stock_list(self):
        lst = [
            "沪深A股",
            "沪深ETF",
            # "沪深基金",
            # "沪深指数",
            "沪深转债",
        ]
        stock_list = []
        for k in lst:
            stock_list.extend(xtdata.get_stock_list_in_sector(k)) 
        return stock_list
    
    def extarp_tick(self, tick: TickData):
        vt_symbol = tick.vt_symbol
        pre_tick: TickData =  self.full_tick.get(vt_symbol)
        if (
            (not pre_tick) or
            (not pre_tick.bid_price_1 and not pre_tick.ask_price_1) or
            (not tick.bid_price_1 and not tick.ask_price_1)
        ):
            return 
        tick.net_amount = tick.amount - pre_tick.amount
        tick.net_volume = tick.volume - pre_tick.volume
        if tick.net_amount == 0:
            return
        # 涨停
        if tick.bid_price_1 and not tick.ask_price_1:
            tick.direction = TickDirection.SELL
        # 跌停
        elif tick.ask_price_1 and not tick.bid_price_1:
            tick.direction = TickDirection.BUY
        # 成交价格突破前tick数据的卖一（主动买）
        elif tick.last_price >= pre_tick.ask_price_1:
            tick.direction = TickDirection.BUY
        # 成交价格突破前tick数据的买一（主动卖）
        elif tick.last_price <= pre_tick.bid_price_1:
            tick.direction = TickDirection.SELL
        # 买盘下移（被动卖）
        elif tick.bid_price_1 < pre_tick.bid_price_1:
                tick.direction = TickDirection.SELL
        # 买盘上移（被动买）
        elif tick.ask_price_1 > pre_tick.ask_price_1:
                tick.direction = TickDirection.BUY
        else:
            mid1 = (pre_tick.ask_price_1 + pre_tick.bid_price_1) / 2
            if tick.last_price >= mid1:
                tick.direction = TickDirection.BUY
            else:
                tick.direction = TickDirection.SELL
        
        return 

    def on_tick(self, data: dict):
        if not self._active:
            return
        ticks : list[TickData] = []
        for vt_symbol, d in data.items():
            symbol, exchange = vt_symbol.split(".")
            timestamp=d["time"]
            bp_data: list = d["bidPrice"]
            ap_data: list = d["askPrice"]
            bv_data: list = d["bidVol"]
            av_data: list = d["askVol"]
            tick: TickData = TickData(
                    symbol=symbol,
                    exchange=exchange,
                    datetime=generate_datetime(timestamp),
                    timestamp=timestamp,
                    last_price=round(d["lastPrice"], 3),
                    open=round(d["open"], 3),
                    high=round(d["high"], 3),
                    low=round(d["low"], 3),
                    pre_close=round(d["lastClose"], 3),
                    volume=d["volume"],
                    amount=d["amount"],
                    bid_price_1=round(bp_data[0], 3),
                    bid_price_2=round(bp_data[1], 3),
                    bid_price_3=round(bp_data[2], 3),
                    bid_price_4=round(bp_data[3], 3),
                    bid_price_5=round(bp_data[4], 3),
                    ask_price_1=round(ap_data[0], 3),
                    ask_price_2=round(ap_data[1], 3),
                    ask_price_3=round(ap_data[2], 3),
                    ask_price_4=round(ap_data[3], 3),
                    ask_price_5=round(ap_data[4], 3),
                    bid_volume_1=bv_data[0],
                    bid_volume_2=bv_data[1],
                    bid_volume_3=bv_data[2],
                    bid_volume_4=bv_data[3],
                    bid_volume_5=bv_data[4],
                    ask_volume_1=av_data[0],
                    ask_volume_2=av_data[1],
                    ask_volume_3=av_data[2],
                    ask_volume_4=av_data[3],
                    ask_volume_5=av_data[4],
                )
            self.extarp_tick(tick)
            ticks.append(tick)
            self.full_tick[vt_symbol] = tick
        for queue in self.queue_list:
            queue.put(ticks)

    def subscribe_whole_quote(self, sleep_time=60):
        stock_list  = self.get_stock_list()
        xtdata.subscribe_whole_quote(
            code_list=stock_list,
            callback=self.on_tick
        )
        time.sleep(sleep_time)
        self._active = True


if __name__ == "__main__":
    qmt_server = QmtServer()
    # qmt_server.strart()
    # qmt_server.test_scp()
    
    # qmt_server.test_strart()



