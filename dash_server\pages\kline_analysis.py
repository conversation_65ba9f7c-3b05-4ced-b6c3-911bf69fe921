"""
K线分析页面 - 专业的K线图表分析
"""

import dash
from dash import dcc, html, Input, Output, callback, State
import dash_bootstrap_components as dbc
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
from pathlib import Path

# 添加项目根目录到路径
root_dir = Path(__file__).parent.parent.parent
sys.path.append(str(root_dir))

from data_service import data_service

# 页面布局
layout = html.Div([
    # 页面标题
    dbc.Row([
        dbc.Col([
            html.H2("📈 K线分析", className="mb-3"),
            html.P("专业的K线图表分析工具，支持多种技术指标", className="text-muted")
        ])
    ], className="mb-4"),
    
    # 控制面板
    dbc.Row([
        dbc.Col([
            dbc.Card([
                dbc.CardHeader("控制面板"),
                dbc.CardBody([
                    # 股票选择
                    html.Label("选择股票:", className="fw-bold mb-2"),
                    dcc.Dropdown(
                        id='kline-stock-selector',
                        options=[
                            {'label': '平安银行 (000001.SZ)', 'value': '000001.SZ'},
                            {'label': '万科A (000002.SZ)', 'value': '000002.SZ'},
                            {'label': '中国平安 (601318.SH)', 'value': '601318.SH'},
                            {'label': '贵州茅台 (600519.SH)', 'value': '600519.SH'},
                            {'label': '招商银行 (600036.SH)', 'value': '600036.SH'},
                        ],
                        value='000001.SZ',
                        className="mb-3"
                    ),
                    
                    # 时间周期
                    html.Label("时间周期:", className="fw-bold mb-2"),
                    dbc.RadioItems(
                        id='kline-interval-selector',
                        options=[
                            {'label': '分钟线', 'value': 'minute'},
                            {'label': '日线', 'value': 'day'},
                            {'label': '周线', 'value': 'week'},
                        ],
                        value='day',
                        className="mb-3"
                    ),
                    
                    # 日期范围
                    html.Label("日期范围:", className="fw-bold mb-2"),
                    dcc.DatePickerRange(
                        id='kline-date-picker',
                        start_date=(datetime.now() - timedelta(days=60)).date(),
                        end_date=datetime.now().date(),
                        display_format='YYYY-MM-DD',
                        className="mb-3"
                    ),
                    
                    # 技术指标
                    html.Label("技术指标:", className="fw-bold mb-2"),
                    dbc.Checklist(
                        id='kline-indicators',
                        options=[
                            {'label': 'MA5', 'value': 'ma5'},
                            {'label': 'MA10', 'value': 'ma10'},
                            {'label': 'MA20', 'value': 'ma20'},
                            {'label': 'MACD', 'value': 'macd'},
                            {'label': 'RSI', 'value': 'rsi'},
                        ],
                        value=['ma5', 'ma10'],
                        className="mb-3"
                    ),
                    
                    # 刷新按钮
                    dbc.Button(
                        "刷新数据",
                        id='kline-refresh-btn',
                        color="primary",
                        className="w-100"
                    )
                ])
            ])
        ], md=3),
        
        # 图表区域
        dbc.Col([
            dbc.Card([
                dbc.CardHeader([
                    html.H5("K线图表", className="mb-0"),
                    html.Small(id="kline-chart-subtitle", className="text-muted")
                ]),
                dbc.CardBody([
                    dcc.Loading(
                        id="kline-loading",
                        children=[
                            dcc.Graph(
                                id='kline-chart',
                                config={
                                    'displayModeBar': True,
                                    'displaylogo': False,
                                    'modeBarButtonsToRemove': ['pan2d', 'lasso2d']
                                }
                            )
                        ],
                        type="default"
                    )
                ])
            ])
        ], md=9)
    ], className="mb-4"),
    
    # 数据表格
    dbc.Row([
        dbc.Col([
            dbc.Card([
                dbc.CardHeader("📊 K线数据详情"),
                dbc.CardBody([
                    html.Div(id="kline-data-table")
                ])
            ])
        ])
    ])
])

# 回调函数
@callback(
    [Output('kline-chart', 'figure'),
     Output('kline-chart-subtitle', 'children'),
     Output('kline-data-table', 'children'),
     Output('selected-stock-store', 'data')],
    [Input('kline-stock-selector', 'value'),
     Input('kline-interval-selector', 'value'),
     Input('kline-date-picker', 'start_date'),
     Input('kline-date-picker', 'end_date'),
     Input('kline-indicators', 'value'),
     Input('kline-refresh-btn', 'n_clicks')]
)
def update_kline_chart(selected_stock, interval, start_date, end_date, indicators, n_clicks):
    """更新K线图表"""
    
    # 更新全局选中股票
    selected_stock_data = selected_stock
    
    # 获取K线数据
    try:
        df = data_service.get_kline_data(
            symbol=selected_stock,
            interval=interval,
            start_date=start_date,
            end_date=end_date
        )
        
        if df.empty:
            empty_fig = go.Figure()
            empty_fig.update_layout(
                title="暂无数据",
                xaxis_title="时间",
                yaxis_title="价格",
                height=600
            )
            return empty_fig, "暂无数据", "暂无数据", selected_stock_data
        
        # 创建子图
        fig = make_subplots(
            rows=3, cols=1,
            shared_xaxes=True,
            vertical_spacing=0.05,
            subplot_titles=('K线图', '成交量', '技术指标'),
            row_heights=[0.6, 0.2, 0.2]
        )
        
        # K线图
        fig.add_trace(
            go.Candlestick(
                x=df['datetime'],
                open=df['open'],
                high=df['high'],
                low=df['low'],
                close=df['close'],
                name='K线',
                increasing_line_color='red',
                decreasing_line_color='green'
            ),
            row=1, col=1
        )
        
        # 添加移动平均线
        if indicators:
            if 'ma5' in indicators and 'ma5' in df.columns:
                fig.add_trace(
                    go.Scatter(
                        x=df['datetime'],
                        y=df['ma5'],
                        mode='lines',
                        name='MA5',
                        line=dict(color='blue', width=1)
                    ),
                    row=1, col=1
                )
            
            if 'ma10' in indicators and 'ma10' in df.columns:
                fig.add_trace(
                    go.Scatter(
                        x=df['datetime'],
                        y=df['ma10'],
                        mode='lines',
                        name='MA10',
                        line=dict(color='orange', width=1)
                    ),
                    row=1, col=1
                )
            
            if 'ma20' in indicators and 'ma20' in df.columns:
                fig.add_trace(
                    go.Scatter(
                        x=df['datetime'],
                        y=df['ma20'],
                        mode='lines',
                        name='MA20',
                        line=dict(color='purple', width=1)
                    ),
                    row=1, col=1
                )
        
        # 成交量
        colors = ['red' if close >= open else 'green' 
                  for close, open in zip(df['close'], df['open'])]
        
        fig.add_trace(
            go.Bar(
                x=df['datetime'],
                y=df['volume'],
                name='成交量',
                marker_color=colors,
                opacity=0.7
            ),
            row=2, col=1
        )
        
        # 技术指标
        if indicators:
            if 'macd' in indicators and 'macd' in df.columns:
                fig.add_trace(
                    go.Scatter(
                        x=df['datetime'],
                        y=df['macd'],
                        mode='lines',
                        name='MACD',
                        line=dict(color='blue', width=1)
                    ),
                    row=3, col=1
                )
                
                fig.add_trace(
                    go.Scatter(
                        x=df['datetime'],
                        y=df['macd_signal'],
                        mode='lines',
                        name='Signal',
                        line=dict(color='red', width=1)
                    ),
                    row=3, col=1
                )
            
            if 'rsi' in indicators and 'rsi' in df.columns:
                fig.add_trace(
                    go.Scatter(
                        x=df['datetime'],
                        y=df['rsi'],
                        mode='lines',
                        name='RSI',
                        line=dict(color='purple', width=1)
                    ),
                    row=3, col=1
                )
                
                # RSI超买超卖线
                fig.add_hline(y=70, line_dash="dash", line_color="red", row=3, col=1)
                fig.add_hline(y=30, line_dash="dash", line_color="green", row=3, col=1)
        
        # 更新布局
        fig.update_layout(
            title=f'{selected_stock} K线分析',
            xaxis_rangeslider_visible=False,
            height=800,
            showlegend=True
        )
        
        # 更新坐标轴
        fig.update_xaxes(title_text="时间", row=3, col=1)
        fig.update_yaxes(title_text="价格", row=1, col=1)
        fig.update_yaxes(title_text="成交量", row=2, col=1)
        fig.update_yaxes(title_text="指标值", row=3, col=1)
        
        # 图表副标题
        subtitle = f"{interval.upper()} | {start_date} 至 {end_date} | 共{len(df)}条数据"
        
        # 数据表格
        latest_data = df.tail(10).round(2)
        table = dbc.Table.from_dataframe(
            latest_data[['datetime', 'open', 'high', 'low', 'close', 'volume']],
            striped=True,
            bordered=True,
            hover=True,
            size='sm'
        )
        
        return fig, subtitle, table, selected_stock_data
        
    except Exception as e:
        print(f"K线图表更新失败: {e}")
        error_fig = go.Figure()
        error_fig.update_layout(
            title=f"数据加载失败: {str(e)}",
            height=600
        )
        return error_fig, "加载失败", f"错误: {str(e)}", selected_stock_data
