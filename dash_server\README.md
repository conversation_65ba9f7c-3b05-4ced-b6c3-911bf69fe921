# 金融量化展示平台 (多页面版本)

基于 Dash 开发的全面金融量化展示平台，采用多页面架构设计，提供实时行情、K线图表、技术指标分析等专业功能。

## 🏗️ 架构特点

### 多页面设计
- **模块化架构**: 每个功能独立成页面，便于维护和扩展
- **路由导航**: 基于URL的页面路由系统
- **响应式布局**: 使用Bootstrap组件，适配各种设备
- **全局数据共享**: 统一的数据管理和状态共享

## 📊 功能页面

### 🏠 首页 (`/`)
- **平台概览**: 欢迎页面和功能导航
- **市场概览**: 实时市场统计和趋势图表
- **快速导航**: 直接跳转到各功能页面

### 📈 K线分析 (`/kline`)
- **专业K线图**: 蜡烛图 + 成交量 + 技术指标
- **多时间周期**: 分钟线、日线、周线
- **技术指标**: MA、MACD、RSI等可选指标
- **数据表格**: 详细的K线数据展示

### 📊 实时行情 (`/market`)
- **实时数据表**: 股票价格、涨跌幅、成交量
- **市场统计**: 涨跌股票数量、平均涨跌幅
- **数据筛选**: 排序、搜索、分页功能
- **涨跌分布**: 市场整体涨跌情况可视化

### 🔍 技术指标 (`/indicators`)
- **专业指标分析**: 深度技术指标分析工具
- **参数自定义**: 可调整指标参数
- **多指标对比**: 同时显示多个技术指标
- **分析报告**: 自动生成技术分析结论

### 📊 市场分析 (`/analysis`)
- **整体市场**: 市场指数、涨跌比、成交统计
- **板块分析**: 不同行业板块表现对比
- **市场情绪**: 恐慌贪婪指数、市场强度
- **热点追踪**: 实时热点板块排行

### 🗄️ 数据管理 (`/data`)
- **数据源管理**: 数据库连接状态监控
- **系统状态**: 性能监控、缓存状态
- **数据质量**: 数据完整性和质量监控
- **数据导出**: 支持多种格式数据导出

### 🎨 界面特性
- **响应式设计**: 适配不同屏幕尺寸
- **实时更新**: 30秒自动刷新数据
- **交互式图表**: 支持缩放、平移等操作
- **数据筛选**: 支持表格排序和筛选
- **美观界面**: 现代化的UI设计

## 安装和运行

### 1. 环境要求
- Python 3.8+
- TDengine 数据库 (可选，无数据库时使用模拟数据)

### 2. 安装依赖
```bash
cd dash_server
pip install -r requirements.txt
```

### 3. 启动应用
```bash
python run.py
```

### 4. 访问应用
打开浏览器访问: http://localhost:8050

## 数据源说明

### 当前支持的数据查询方法
- `load_bar_data()`: 获取K线数据
- `get_full_tick()`: 获取实时tick数据
- `get_tick_by_vt_symbol()`: 获取指定股票的tick数据

### 需要补充的数据查询方法
以下功能需要在数据库中添加相应的查询方法：

1. **股票列表查询**
   ```python
   def get_stock_list() -> List[Dict]:
       """获取所有股票列表，包含股票代码、名称、交易所等信息"""
       pass
   ```

2. **股票基本信息查询**
   ```python
   def get_stock_info(symbol: str) -> Dict:
       """获取股票基本信息，如公司名称、行业、市值等"""
       pass
   ```

3. **板块信息查询**
   ```python
   def get_sector_data() -> List[Dict]:
       """获取板块分类和股票归属信息"""
       pass
   ```

4. **历史数据统计**
   ```python
   def get_market_statistics(date: str) -> Dict:
       """获取指定日期的市场统计数据"""
       pass
   ```

## 📁 项目结构

```
dash_server/
├── main_app.py           # 多页面主应用文件 (推荐)
├── app.py               # 单页面应用文件 (备用)
├── test_app.py          # 简化测试版本
├── data_service.py      # 数据服务模块
├── components.py        # UI组件模块
├── pages/               # 页面模块目录
│   ├── __init__.py
│   ├── home.py          # 首页
│   ├── kline_analysis.py      # K线分析页
│   ├── real_time_market.py    # 实时行情页
│   ├── technical_indicators.py # 技术指标页
│   ├── market_analysis.py     # 市场分析页
│   └── data_management.py     # 数据管理页
├── run.py              # 智能启动脚本
├── requirements.txt    # 依赖包列表
├── README.md          # 说明文档
└── 功能说明.md        # 功能详细说明
```

## 配置说明

### 数据库配置
数据库配置在 `utils/setting.py` 中设置，主要参数：
- 数据库地址
- 用户名密码
- 数据库名称

### 应用配置
可在 `app.py` 中修改：
- 端口号 (默认8050)
- 调试模式
- 自动刷新间隔

## 使用说明

### 1. 股票选择
在左侧控制面板选择要查看的股票代码

### 2. 时间周期
选择K线图的时间周期：分钟线、日线、周线

### 3. 日期范围
设置查询的起始和结束日期

### 4. 技术指标
勾选要显示的技术指标：MA5、MA10、MA20、MACD、RSI

### 5. 数据刷新
- 点击"刷新数据"按钮手动刷新
- 系统每30秒自动刷新一次

## 技术栈

- **前端框架**: Dash + Plotly
- **数据处理**: Pandas + NumPy
- **数据库**: TDengine
- **图表库**: Plotly.js
- **样式**: CSS3

## 扩展功能

### 计划中的功能
- [ ] 自选股管理
- [ ] 预警系统
- [ ] 回测功能
- [ ] 更多技术指标
- [ ] 数据导出
- [ ] 用户权限管理

### 自定义开发
可以通过以下方式扩展功能：
1. 在 `components.py` 中添加新的UI组件
2. 在 `data_service.py` 中添加新的数据查询方法
3. 在 `app.py` 中添加新的回调函数

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库服务是否启动
   - 确认连接参数是否正确
   - 系统会自动切换到模拟数据模式

2. **端口被占用**
   - 修改 `run.py` 中的端口号
   - 或者停止占用8050端口的其他服务

3. **依赖包安装失败**
   - 使用国内镜像源：`pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt`
   - 检查Python版本是否符合要求

## 联系方式

如有问题或建议，请联系开发团队。

---

**注意**: 当前版本在没有真实数据时会使用模拟数据，确保应用能够正常运行和演示。
