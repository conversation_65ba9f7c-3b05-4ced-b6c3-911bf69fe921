"""
技术指标页面 - 专业技术指标分析
"""

import dash
from dash import dcc, html, Input, Output, callback
import dash_bootstrap_components as dbc
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
from pathlib import Path

# 添加项目根目录到路径
root_dir = Path(__file__).parent.parent.parent
sys.path.append(str(root_dir))

from data_service import data_service

# 页面布局
layout = html.Div([
    # 页面标题
    dbc.Row([
        dbc.Col([
            html.H2("🔍 技术指标分析", className="mb-3"),
            html.P("专业的技术指标分析工具", className="text-muted")
        ])
    ], className="mb-4"),
    
    # 控制面板
    dbc.Row([
        dbc.Col([
            dbc.Card([
                dbc.CardHeader("参数设置"),
                dbc.CardBody([
                    # 股票选择
                    html.Label("选择股票:", className="fw-bold mb-2"),
                    dcc.Dropdown(
                        id='indicators-stock-selector',
                        options=[
                            {'label': '平安银行 (000001.SZ)', 'value': '000001.SZ'},
                            {'label': '万科A (000002.SZ)', 'value': '000002.SZ'},
                            {'label': '中国平安 (601318.SH)', 'value': '601318.SH'},
                            {'label': '贵州茅台 (600519.SH)', 'value': '600519.SH'},
                            {'label': '招商银行 (600036.SH)', 'value': '600036.SH'},
                        ],
                        value='000001.SZ',
                        className="mb-3"
                    ),
                    
                    # 时间周期
                    html.Label("时间周期:", className="fw-bold mb-2"),
                    dbc.RadioItems(
                        id='indicators-interval',
                        options=[
                            {'label': '日线', 'value': 'day'},
                            {'label': '周线', 'value': 'week'},
                        ],
                        value='day',
                        className="mb-3"
                    ),
                    
                    # 指标选择
                    html.Label("选择指标:", className="fw-bold mb-2"),
                    dbc.Checklist(
                        id='indicators-selection',
                        options=[
                            {'label': '移动平均线 (MA)', 'value': 'ma'},
                            {'label': 'MACD', 'value': 'macd'},
                            {'label': 'RSI', 'value': 'rsi'},
                            {'label': '布林带 (BOLL)', 'value': 'boll'},
                        ],
                        value=['ma', 'macd'],
                        className="mb-3"
                    ),
                    
                    # MA参数
                    html.Div([
                        html.Label("MA周期:", className="fw-bold mb-2"),
                        dcc.RangeSlider(
                            id='ma-periods',
                            min=5,
                            max=60,
                            step=5,
                            value=[5, 20],
                            marks={i: str(i) for i in range(5, 61, 10)},
                            className="mb-3"
                        )
                    ], id='ma-params'),
                    
                    # RSI参数
                    html.Div([
                        html.Label("RSI周期:", className="fw-bold mb-2"),
                        dcc.Slider(
                            id='rsi-period',
                            min=6,
                            max=24,
                            step=2,
                            value=14,
                            marks={i: str(i) for i in range(6, 25, 6)},
                            className="mb-3"
                        )
                    ], id='rsi-params', style={'display': 'none'}),
                    
                    # 刷新按钮
                    dbc.Button(
                        "更新分析",
                        id='indicators-update-btn',
                        color="primary",
                        className="w-100"
                    )
                ])
            ])
        ], md=3),
        
        # 图表区域
        dbc.Col([
            dbc.Card([
                dbc.CardHeader("技术指标图表"),
                dbc.CardBody([
                    dcc.Loading(
                        id="indicators-loading",
                        children=[
                            dcc.Graph(
                                id='indicators-chart',
                                config={'displayModeBar': True, 'displaylogo': False}
                            )
                        ],
                        type="default"
                    )
                ])
            ])
        ], md=9)
    ], className="mb-4"),
    
    # 指标说明和分析
    dbc.Row([
        dbc.Col([
            dbc.Card([
                dbc.CardHeader("📈 指标分析"),
                dbc.CardBody([
                    html.Div(id="indicators-analysis")
                ])
            ])
        ], md=6),
        
        dbc.Col([
            dbc.Card([
                dbc.CardHeader("📚 指标说明"),
                dbc.CardBody([
                    html.Div(id="indicators-explanation")
                ])
            ])
        ], md=6)
    ])
])

# 回调函数 - 显示/隐藏参数设置
@callback(
    [Output('ma-params', 'style'),
     Output('rsi-params', 'style')],
    Input('indicators-selection', 'value')
)
def toggle_params(selected_indicators):
    """根据选择的指标显示/隐藏参数设置"""
    ma_style = {'display': 'block'} if 'ma' in selected_indicators else {'display': 'none'}
    rsi_style = {'display': 'block'} if 'rsi' in selected_indicators else {'display': 'none'}
    return ma_style, rsi_style

# 主要回调函数
@callback(
    [Output('indicators-chart', 'figure'),
     Output('indicators-analysis', 'children'),
     Output('indicators-explanation', 'children')],
    [Input('indicators-stock-selector', 'value'),
     Input('indicators-interval', 'value'),
     Input('indicators-selection', 'value'),
     Input('ma-periods', 'value'),
     Input('rsi-period', 'value'),
     Input('indicators-update-btn', 'n_clicks')]
)
def update_indicators(stock, interval, selected_indicators, ma_periods, rsi_period, n_clicks):
    """更新技术指标分析"""
    
    if not selected_indicators:
        empty_fig = go.Figure()
        empty_fig.update_layout(title="请选择要分析的技术指标", height=600)
        return empty_fig, "请选择技术指标", "请选择技术指标"
    
    try:
        # 获取数据
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=180)).strftime('%Y-%m-%d')
        
        df = data_service.get_kline_data(
            symbol=stock,
            interval=interval,
            start_date=start_date,
            end_date=end_date
        )
        
        if df.empty:
            empty_fig = go.Figure()
            empty_fig.update_layout(title="暂无数据", height=600)
            return empty_fig, "暂无数据", "暂无数据"
        
        # 计算额外的技术指标
        df = calculate_additional_indicators(df, ma_periods, rsi_period)
        
        # 创建子图
        subplot_count = len(selected_indicators) + 1  # +1 for price chart
        fig = make_subplots(
            rows=subplot_count,
            cols=1,
            shared_xaxes=True,
            vertical_spacing=0.05,
            subplot_titles=['价格图表'] + [get_indicator_title(ind) for ind in selected_indicators],
            row_heights=[0.4] + [0.6/len(selected_indicators)] * len(selected_indicators)
        )
        
        # 价格图表
        fig.add_trace(
            go.Candlestick(
                x=df['datetime'],
                open=df['open'],
                high=df['high'],
                low=df['low'],
                close=df['close'],
                name='K线'
            ),
            row=1, col=1
        )
        
        # 添加各种指标
        row_idx = 2
        for indicator in selected_indicators:
            if indicator == 'ma':
                add_ma_indicators(fig, df, ma_periods, row=1)
            elif indicator == 'macd':
                add_macd_indicators(fig, df, row=row_idx)
                row_idx += 1
            elif indicator == 'rsi':
                add_rsi_indicators(fig, df, rsi_period, row=row_idx)
                row_idx += 1
            elif indicator == 'boll':
                add_boll_indicators(fig, df, row=1)
        
        # 更新布局
        fig.update_layout(
            title=f'{stock} 技术指标分析',
            height=200 * subplot_count + 200,
            showlegend=True,
            xaxis_rangeslider_visible=False
        )
        
        # 生成分析报告
        analysis = generate_analysis_report(df, selected_indicators)
        
        # 生成指标说明
        explanation = generate_indicator_explanation(selected_indicators)
        
        return fig, analysis, explanation
        
    except Exception as e:
        print(f"技术指标分析失败: {e}")
        error_fig = go.Figure()
        error_fig.update_layout(title=f"分析失败: {str(e)}", height=600)
        return error_fig, f"分析失败: {str(e)}", "分析失败"

def calculate_additional_indicators(df, ma_periods, rsi_period):
    """计算额外的技术指标"""
    # 布林带
    df['bb_middle'] = df['close'].rolling(window=20).mean()
    bb_std = df['close'].rolling(window=20).std()
    df['bb_upper'] = df['bb_middle'] + (bb_std * 2)
    df['bb_lower'] = df['bb_middle'] - (bb_std * 2)
    
    # 自定义MA
    for period in ma_periods:
        df[f'ma_{period}'] = df['close'].rolling(window=period).mean()
    
    return df

def get_indicator_title(indicator):
    """获取指标标题"""
    titles = {
        'ma': '移动平均线',
        'macd': 'MACD',
        'rsi': 'RSI',
        'boll': '布林带'
    }
    return titles.get(indicator, indicator)

def add_ma_indicators(fig, df, periods, row):
    """添加移动平均线"""
    colors = ['blue', 'orange', 'purple', 'brown', 'pink']
    for i, period in enumerate(periods):
        if f'ma_{period}' in df.columns:
            fig.add_trace(
                go.Scatter(
                    x=df['datetime'],
                    y=df[f'ma_{period}'],
                    mode='lines',
                    name=f'MA{period}',
                    line=dict(color=colors[i % len(colors)], width=1)
                ),
                row=row, col=1
            )

def add_macd_indicators(fig, df, row):
    """添加MACD指标"""
    if 'macd' in df.columns:
        fig.add_trace(
            go.Scatter(
                x=df['datetime'],
                y=df['macd'],
                mode='lines',
                name='MACD',
                line=dict(color='blue')
            ),
            row=row, col=1
        )
        
        fig.add_trace(
            go.Scatter(
                x=df['datetime'],
                y=df['macd_signal'],
                mode='lines',
                name='Signal',
                line=dict(color='red')
            ),
            row=row, col=1
        )
        
        fig.add_trace(
            go.Bar(
                x=df['datetime'],
                y=df['macd_histogram'],
                name='Histogram',
                marker_color='gray',
                opacity=0.6
            ),
            row=row, col=1
        )

def add_rsi_indicators(fig, df, period, row):
    """添加RSI指标"""
    if 'rsi' in df.columns:
        fig.add_trace(
            go.Scatter(
                x=df['datetime'],
                y=df['rsi'],
                mode='lines',
                name=f'RSI({period})',
                line=dict(color='purple')
            ),
            row=row, col=1
        )
        
        # 超买超卖线
        fig.add_hline(y=70, line_dash="dash", line_color="red", row=row, col=1)
        fig.add_hline(y=30, line_dash="dash", line_color="green", row=row, col=1)

def add_boll_indicators(fig, df, row):
    """添加布林带指标"""
    if 'bb_upper' in df.columns:
        fig.add_trace(
            go.Scatter(
                x=df['datetime'],
                y=df['bb_upper'],
                mode='lines',
                name='布林上轨',
                line=dict(color='red', dash='dash')
            ),
            row=row, col=1
        )
        
        fig.add_trace(
            go.Scatter(
                x=df['datetime'],
                y=df['bb_middle'],
                mode='lines',
                name='布林中轨',
                line=dict(color='blue')
            ),
            row=row, col=1
        )
        
        fig.add_trace(
            go.Scatter(
                x=df['datetime'],
                y=df['bb_lower'],
                mode='lines',
                name='布林下轨',
                line=dict(color='green', dash='dash')
            ),
            row=row, col=1
        )

def generate_analysis_report(df, indicators):
    """生成分析报告"""
    if df.empty:
        return "暂无数据"
    
    latest = df.iloc[-1]
    analysis = []
    
    analysis.append(html.H5("最新分析结果"))
    analysis.append(html.P(f"最新价格: ¥{latest['close']:.2f}"))
    
    if 'ma' in indicators:
        if 'ma_5' in df.columns and 'ma_20' in df.columns:
            ma5 = latest['ma_5']
            ma20 = latest['ma_20']
            if ma5 > ma20:
                analysis.append(html.P("✅ MA5 > MA20，短期趋势向上", className="text-success"))
            else:
                analysis.append(html.P("❌ MA5 < MA20，短期趋势向下", className="text-danger"))
    
    if 'rsi' in indicators and 'rsi' in df.columns:
        rsi = latest['rsi']
        if rsi > 70:
            analysis.append(html.P(f"⚠️ RSI({rsi:.1f}) > 70，可能超买", className="text-warning"))
        elif rsi < 30:
            analysis.append(html.P(f"⚠️ RSI({rsi:.1f}) < 30，可能超卖", className="text-warning"))
        else:
            analysis.append(html.P(f"✅ RSI({rsi:.1f}) 处于正常区间", className="text-info"))
    
    if 'macd' in indicators and 'macd' in df.columns:
        macd = latest['macd']
        signal = latest['macd_signal']
        if macd > signal:
            analysis.append(html.P("✅ MACD > Signal，多头信号", className="text-success"))
        else:
            analysis.append(html.P("❌ MACD < Signal，空头信号", className="text-danger"))
    
    return analysis

def generate_indicator_explanation(indicators):
    """生成指标说明"""
    explanations = {
        'ma': html.Div([
            html.H6("移动平均线 (MA)"),
            html.P("移动平均线是技术分析中最基本的指标，用于平滑价格波动，识别趋势方向。")
        ]),
        'macd': html.Div([
            html.H6("MACD"),
            html.P("MACD是趋势跟踪动量指标，通过计算两条不同速度的指数移动平均线的差值来判断买卖时机。")
        ]),
        'rsi': html.Div([
            html.H6("相对强弱指数 (RSI)"),
            html.P("RSI是动量振荡器，用于测量价格变动的速度和变化，识别超买超卖状态。")
        ]),
        'boll': html.Div([
            html.H6("布林带 (BOLL)"),
            html.P("布林带由移动平均线和标准差构成，用于判断价格的相对高低位置。")
        ])
    }
    
    result = []
    for indicator in indicators:
        if indicator in explanations:
            result.append(explanations[indicator])
            result.append(html.Hr())
    
    return result if result else "请选择技术指标"
