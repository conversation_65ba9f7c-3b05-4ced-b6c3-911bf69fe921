"""
首页 - 平台概览和快速导航
"""

import dash
from dash import dcc, html, Input, Output, callback
import plotly.graph_objects as go
import plotly.express as px
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
from pathlib import Path

# 尝试导入Bootstrap组件
try:
    import dash_bootstrap_components as dbc
    HAS_BOOTSTRAP = True
except ImportError:
    HAS_BOOTSTRAP = False
    # 简单的替代组件
    class dbc:
        @staticmethod
        def Jumbotron(children, **kwargs):
            return html.Div(children, style={'background-color': '#e9ecef', 'padding': '40px', 'border-radius': '10px', 'margin': '20px 0'})

        @staticmethod
        def Row(children, **kwargs):
            return html.Div(children, style={'display': 'flex', 'flex-wrap': 'wrap', 'margin': '0 -15px'})

        @staticmethod
        def Col(children, **kwargs):
            md = kwargs.get('md', 12)
            width = f"{(md/12)*100}%"
            return html.Div(children, style={'flex': f'0 0 {width}', 'max-width': width, 'padding': '0 15px'})

        @staticmethod
        def Card(children, **kwargs):
            return html.Div(children, style={'border': '1px solid #ddd', 'border-radius': '5px', 'margin': '10px 0', 'height': '100%'})

        @staticmethod
        def CardHeader(children):
            return html.Div(children, style={'background-color': '#f8f9fa', 'padding': '10px', 'border-bottom': '1px solid #ddd', 'font-weight': 'bold'})

        @staticmethod
        def CardBody(children):
            return html.Div(children, style={'padding': '15px'})

        @staticmethod
        def Button(children, **kwargs):
            color = kwargs.get('color', 'primary')
            colors = {'primary': '#007bff', 'success': '#28a745', 'info': '#17a2b8'}
            bg_color = colors.get(color, '#007bff')
            return html.Button(children, style={'padding': '8px 16px', 'border': 'none', 'border-radius': '4px', 'background-color': bg_color, 'color': 'white', 'cursor': 'pointer', 'text-decoration': 'none', 'display': 'inline-block'})

# 添加项目根目录到路径
root_dir = Path(__file__).parent.parent.parent
sys.path.append(str(root_dir))

from data_service import data_service

# 页面布局
layout = html.Div([
    # 欢迎横幅
    dbc.Jumbotron([
        html.H1("欢迎使用金融量化展示平台", className="display-4"),
        html.P("专业的股票分析和实时行情展示系统", className="lead"),
        html.Hr(className="my-2"),
        html.P("提供K线分析、技术指标、实时行情等全方位金融数据服务"),
        dbc.Button("开始使用", color="primary", size="lg", href="/kline")
    ], className="mb-4"),
    
    # 快速统计卡片
    dbc.Row([
        dbc.Col([
            dbc.Card([
                dbc.CardBody([
                    html.H4("📈", className="card-title text-center"),
                    html.H5("K线分析", className="card-title text-center"),
                    html.P("多时间周期K线图表分析", className="card-text text-center"),
                    dbc.Button("进入", color="primary", href="/kline", className="d-block mx-auto")
                ])
            ], className="h-100")
        ], md=4),
        
        dbc.Col([
            dbc.Card([
                dbc.CardBody([
                    html.H4("📊", className="card-title text-center"),
                    html.H5("实时行情", className="card-title text-center"),
                    html.P("实时股票价格和成交数据", className="card-text text-center"),
                    dbc.Button("进入", color="success", href="/market", className="d-block mx-auto")
                ])
            ], className="h-100")
        ], md=4),
        
        dbc.Col([
            dbc.Card([
                dbc.CardBody([
                    html.H4("🔍", className="card-title text-center"),
                    html.H5("技术指标", className="card-title text-center"),
                    html.P("专业技术指标分析工具", className="card-text text-center"),
                    dbc.Button("进入", color="info", href="/indicators", className="d-block mx-auto")
                ])
            ], className="h-100")
        ], md=4),
    ], className="mb-4"),
    
    # 市场概览
    dbc.Row([
        dbc.Col([
            dbc.Card([
                dbc.CardHeader("📊 市场概览"),
                dbc.CardBody([
                    html.Div(id="home-market-overview")
                ])
            ])
        ], md=8),
        
        dbc.Col([
            dbc.Card([
                dbc.CardHeader("🏆 今日排行"),
                dbc.CardBody([
                    html.Div(id="home-top-stocks")
                ])
            ])
        ], md=4),
    ], className="mb-4"),
    
    # 快速图表
    dbc.Row([
        dbc.Col([
            dbc.Card([
                dbc.CardHeader("📈 市场趋势"),
                dbc.CardBody([
                    dcc.Graph(id="home-trend-chart")
                ])
            ])
        ])
    ])
])

# 回调函数
@callback(
    [Output('home-market-overview', 'children'),
     Output('home-top-stocks', 'children'),
     Output('home-trend-chart', 'figure')],
    Input('global-data-store', 'data')
)
def update_home_page(global_data):
    """更新首页数据"""
    if not global_data or not global_data.get('market_data'):
        return "加载中...", "加载中...", {}
    
    market_data = global_data['market_data']
    
    # 市场概览
    total_stocks = len(market_data)
    up_stocks = len([d for d in market_data if d.get('change', 0) > 0])
    down_stocks = len([d for d in market_data if d.get('change', 0) < 0])
    flat_stocks = total_stocks - up_stocks - down_stocks
    
    market_overview = dbc.Row([
        dbc.Col([
            html.H4(str(total_stocks), className="text-primary"),
            html.P("总股票数", className="text-muted")
        ], className="text-center"),
        dbc.Col([
            html.H4(str(up_stocks), className="text-danger"),
            html.P("上涨", className="text-muted")
        ], className="text-center"),
        dbc.Col([
            html.H4(str(down_stocks), className="text-success"),
            html.P("下跌", className="text-muted")
        ], className="text-center"),
        dbc.Col([
            html.H4(str(flat_stocks), className="text-secondary"),
            html.P("平盘", className="text-muted")
        ], className="text-center"),
    ])
    
    # 涨幅排行
    sorted_stocks = sorted(market_data, key=lambda x: x.get('change_pct', 0), reverse=True)
    top_stocks = []
    for i, stock in enumerate(sorted_stocks[:5]):
        color = "text-danger" if stock.get('change_pct', 0) > 0 else "text-success"
        top_stocks.append(
            html.Div([
                html.Span(f"{i+1}. ", className="text-muted"),
                html.Span(stock.get('symbol', ''), className="fw-bold"),
                html.Span(f" {stock.get('change_pct', 0):.2%}", className=color)
            ], className="mb-1")
        )
    
    # 趋势图表
    # 生成模拟的市场指数数据
    dates = pd.date_range(start=datetime.now() - timedelta(days=30), periods=30, freq='D')
    index_values = np.random.randn(30).cumsum() + 3000
    
    fig = go.Figure()
    fig.add_trace(go.Scatter(
        x=dates,
        y=index_values,
        mode='lines',
        name='市场指数',
        line=dict(color='#1f77b4', width=2)
    ))
    
    fig.update_layout(
        title="30日市场趋势",
        xaxis_title="日期",
        yaxis_title="指数",
        height=300,
        margin=dict(l=20, r=20, t=40, b=20),
        showlegend=False
    )
    
    return market_overview, top_stocks, fig
