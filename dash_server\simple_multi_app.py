"""
简化版多页面金融量化展示平台 - 不依赖Bootstrap
"""

import dash
from dash import dcc, html, Input, Output, callback
import plotly.graph_objects as go
import plotly.express as px
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
from pathlib import Path

# 添加项目根目录到路径
root_dir = Path(__file__).parent.parent
sys.path.append(str(root_dir))

from data_service import data_service

# 初始化Dash应用
app = dash.Dash(__name__, suppress_callback_exceptions=True)
app.title = "金融量化展示平台"

# 样式定义
NAVBAR_STYLE = {
    'background-color': '#007bff',
    'padding': '10px 20px',
    'margin-bottom': '20px',
    'display': 'flex',
    'justify-content': 'space-between',
    'align-items': 'center'
}

NAV_LINK_STYLE = {
    'color': 'white',
    'text-decoration': 'none',
    'padding': '5px 15px',
    'margin': '0 5px',
    'border-radius': '3px',
    'display': 'inline-block'
}

CARD_STYLE = {
    'border': '1px solid #ddd',
    'border-radius': '8px',
    'margin': '10px',
    'padding': '20px',
    'box-shadow': '0 2px 4px rgba(0,0,0,0.1)'
}

# 导航栏组件
def create_navbar():
    """创建导航栏"""
    return html.Nav([
        html.Div([
            html.H3("📊 金融量化平台", style={'color': 'white', 'margin': '0'}),
            html.Div([
                html.A("首页", href="/", style=NAV_LINK_STYLE),
                html.A("K线分析", href="/kline", style=NAV_LINK_STYLE),
                html.A("实时行情", href="/market", style=NAV_LINK_STYLE),
                html.A("技术指标", href="/indicators", style=NAV_LINK_STYLE),
                html.A("市场分析", href="/analysis", style=NAV_LINK_STYLE),
            ], style={'display': 'flex'})
        ], style={'display': 'flex', 'justify-content': 'space-between', 'align-items': 'center', 'width': '100%'})
    ], style=NAVBAR_STYLE)

# 首页布局
def create_home_layout():
    """创建首页布局"""
    return html.Div([
        # 欢迎区域
        html.Div([
            html.H1("欢迎使用金融量化展示平台", style={'text-align': 'center', 'color': '#333'}),
            html.P("专业的股票分析和实时行情展示系统", style={'text-align': 'center', 'color': '#666', 'font-size': '18px'}),
        ], style={'background-color': '#f8f9fa', 'padding': '40px', 'border-radius': '10px', 'margin': '20px 0'}),
        
        # 功能卡片
        html.Div([
            html.Div([
                html.H4("📈 K线分析", style={'color': '#007bff'}),
                html.P("多时间周期K线图表分析"),
                html.A("进入", href="/kline", style={'background-color': '#007bff', 'color': 'white', 'padding': '8px 16px', 'text-decoration': 'none', 'border-radius': '4px'})
            ], style={**CARD_STYLE, 'width': '30%', 'display': 'inline-block', 'vertical-align': 'top'}),
            
            html.Div([
                html.H4("📊 实时行情", style={'color': '#28a745'}),
                html.P("实时股票价格和成交数据"),
                html.A("进入", href="/market", style={'background-color': '#28a745', 'color': 'white', 'padding': '8px 16px', 'text-decoration': 'none', 'border-radius': '4px'})
            ], style={**CARD_STYLE, 'width': '30%', 'display': 'inline-block', 'vertical-align': 'top'}),
            
            html.Div([
                html.H4("🔍 技术指标", style={'color': '#17a2b8'}),
                html.P("专业技术指标分析工具"),
                html.A("进入", href="/indicators", style={'background-color': '#17a2b8', 'color': 'white', 'padding': '8px 16px', 'text-decoration': 'none', 'border-radius': '4px'})
            ], style={**CARD_STYLE, 'width': '30%', 'display': 'inline-block', 'vertical-align': 'top'}),
        ], style={'text-align': 'center'}),
        
        # 市场概览
        html.Div([
            html.H3("📊 市场概览"),
            html.Div(id="home-market-overview")
        ], style=CARD_STYLE),
        
        # 趋势图表
        html.Div([
            html.H3("📈 市场趋势"),
            dcc.Graph(id="home-trend-chart")
        ], style=CARD_STYLE)
    ])

# K线分析页面布局
def create_kline_layout():
    """创建K线分析页面布局"""
    return html.Div([
        html.H2("📈 K线分析"),
        
        # 控制面板
        html.Div([
            html.Div([
                html.Label("选择股票:"),
                dcc.Dropdown(
                    id='kline-stock-selector',
                    options=[
                        {'label': '平安银行 (000001.SZ)', 'value': '000001.SZ'},
                        {'label': '万科A (000002.SZ)', 'value': '000002.SZ'},
                        {'label': '中国平安 (601318.SH)', 'value': '601318.SH'},
                        {'label': '贵州茅台 (600519.SH)', 'value': '600519.SH'},
                    ],
                    value='000001.SZ'
                )
            ], style={'width': '30%', 'display': 'inline-block', 'margin': '10px'}),
            
            html.Div([
                html.Label("时间周期:"),
                dcc.Dropdown(
                    id='kline-interval',
                    options=[
                        {'label': '日线', 'value': 'day'},
                        {'label': '周线', 'value': 'week'},
                    ],
                    value='day'
                )
            ], style={'width': '30%', 'display': 'inline-block', 'margin': '10px'}),
            
            html.Div([
                html.Button("刷新数据", id='kline-refresh', style={'background-color': '#007bff', 'color': 'white', 'border': 'none', 'padding': '8px 16px', 'border-radius': '4px'})
            ], style={'width': '30%', 'display': 'inline-block', 'margin': '10px', 'text-align': 'center'})
        ]),
        
        # K线图表
        html.Div([
            dcc.Loading(
                dcc.Graph(id='kline-chart'),
                type="default"
            )
        ], style=CARD_STYLE)
    ])

# 实时行情页面布局
def create_market_layout():
    """创建实时行情页面布局"""
    return html.Div([
        html.H2("📊 实时行情"),
        
        # 市场统计
        html.Div([
            html.Div([
                html.H4(id="market-total", style={'color': '#007bff'}),
                html.P("总股票数")
            ], style={'width': '25%', 'display': 'inline-block', 'text-align': 'center'}),
            
            html.Div([
                html.H4(id="market-up", style={'color': '#dc3545'}),
                html.P("上涨")
            ], style={'width': '25%', 'display': 'inline-block', 'text-align': 'center'}),
            
            html.Div([
                html.H4(id="market-down", style={'color': '#28a745'}),
                html.P("下跌")
            ], style={'width': '25%', 'display': 'inline-block', 'text-align': 'center'}),
            
            html.Div([
                html.H4(id="market-avg", style={'color': '#ffc107'}),
                html.P("平均涨跌幅")
            ], style={'width': '25%', 'display': 'inline-block', 'text-align': 'center'}),
        ], style=CARD_STYLE),
        
        # 行情表格
        html.Div([
            html.H3("实时行情数据"),
            html.Div(id="market-table")
        ], style=CARD_STYLE)
    ])

# 主布局
app.layout = html.Div([
    dcc.Location(id='url', refresh=False),
    create_navbar(),
    html.Div(id='page-content', style={'padding': '20px'}),
    
    # 全局存储
    dcc.Store(id='global-data-store'),
    
    # 定时器
    dcc.Interval(
        id='global-interval',
        interval=30*1000,
        n_intervals=0
    )
])

# 页面路由回调
@app.callback(
    Output('page-content', 'children'),
    Input('url', 'pathname')
)
def display_page(pathname):
    """根据URL显示对应页面"""
    if pathname == '/kline':
        return create_kline_layout()
    elif pathname == '/market':
        return create_market_layout()
    elif pathname == '/indicators':
        return html.Div([
            html.H2("🔍 技术指标分析"),
            html.P("技术指标分析功能开发中..."),
            html.A("返回首页", href="/", style=NAV_LINK_STYLE)
        ])
    elif pathname == '/analysis':
        return html.Div([
            html.H2("📊 市场分析"),
            html.P("市场分析功能开发中..."),
            html.A("返回首页", href="/", style=NAV_LINK_STYLE)
        ])
    else:
        return create_home_layout()

# 全局数据更新回调
@app.callback(
    Output('global-data-store', 'data'),
    Input('global-interval', 'n_intervals')
)
def update_global_data(n_intervals):
    """更新全局数据"""
    try:
        market_data = data_service.get_tick_data(limit=50)
        return {'market_data': market_data, 'last_update': n_intervals}
    except Exception as e:
        print(f"全局数据更新失败: {e}")
        return {'market_data': [], 'last_update': n_intervals, 'error': str(e)}

# 首页回调
@app.callback(
    [Output('home-market-overview', 'children'),
     Output('home-trend-chart', 'figure')],
    Input('global-data-store', 'data')
)
def update_home(global_data):
    """更新首页"""
    if not global_data or not global_data.get('market_data'):
        return "加载中...", {}
    
    market_data = global_data['market_data']
    total = len(market_data)
    up = len([d for d in market_data if d.get('change', 0) > 0])
    down = len([d for d in market_data if d.get('change', 0) < 0])
    
    overview = html.Div([
        html.P(f"总股票数: {total}"),
        html.P(f"上涨: {up}", style={'color': 'red'}),
        html.P(f"下跌: {down}", style={'color': 'green'}),
    ])
    
    # 简单趋势图
    dates = pd.date_range(start=datetime.now() - timedelta(days=30), periods=30, freq='D')
    values = np.random.randn(30).cumsum() + 3000
    
    fig = go.Figure()
    fig.add_trace(go.Scatter(x=dates, y=values, mode='lines', name='市场指数'))
    fig.update_layout(title="30日市场趋势", height=300)
    
    return overview, fig

if __name__ == '__main__':
    app.run(debug=True, host='127.0.0.1', port=8050)
