from database import taosws_client

db_client = taosws_client

# vt_symbol = "300575.SZ"
# d = db_client.get_tick_by_vt_symbol(vt_symbol)
# print(d, len(d))
# d = db_client.stmt2_query()
# d = db_client.get_full_tick()  partition by vt_symbol

sql = f"select vt_symbol,mine.* from real_data.mine where datetime > NOW-4d" 
res= []
with db_client.connection() as conn:
    cursor = conn.cursor()
    r = cursor.execute(sql)
    res = cursor.fetch_all_into_dict()
    print(len(res))
print(res[0])