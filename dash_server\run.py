"""
启动脚本 - 运行金融量化展示平台
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
root_dir = Path(__file__).parent.parent
sys.path.append(str(root_dir))

def main():
    """主函数"""
    try:
        print("正在启动金融量化展示平台...")
        print("=" * 50)

        # 检查Python版本
        import sys
        if sys.version_info < (3, 8):
            print("❌ Python版本过低，需要Python 3.8+")
            return
        print(f"✓ Python版本: {sys.version}")

        # 检查依赖
        missing_deps = []
        try:
            import dash
            print(f"✓ Dash版本: {dash.__version__}")
        except ImportError:
            missing_deps.append("dash")

        try:
            import plotly
            print(f"✓ Plotly版本: {plotly.__version__}")
        except ImportError:
            missing_deps.append("plotly")

        try:
            import pandas
            print(f"✓ Pandas版本: {pandas.__version__}")
        except ImportError:
            missing_deps.append("pandas")

        try:
            import numpy
            print(f"✓ NumPy版本: {numpy.__version__}")
        except ImportError:
            missing_deps.append("numpy")

        if missing_deps:
            print(f"✗ 缺少依赖: {', '.join(missing_deps)}")
            print("请运行: pip install -r requirements.txt")
            return

        print("✓ 所有依赖检查通过")

        # 检查数据库连接
        try:
            from database.taosws_database import TaoswsDatabase
            db_client = TaoswsDatabase()
            print("✓ 数据库连接成功")
        except Exception as e:
            print(f"⚠ 数据库连接失败: {e}")
            print("将使用模拟数据运行")

        # 启动应用 - 按优先级尝试不同版本
        app = None

        # 1. 尝试简化多页面版本 (不依赖Bootstrap)
        try:
            from simple_multi_app import app
            print("✓ 简化多页面应用初始化完成")
        except Exception as e:
            print(f"❌ 简化多页面应用失败: {e}")

            # 2. 尝试完整多页面版本
            try:
                from main_app import app
                print("✓ 完整多页面应用初始化完成")
            except Exception as e2:
                print(f"❌ 完整多页面应用失败: {e2}")

                # 3. 尝试单页面版本
                try:
                    from app import app
                    print("✓ 单页面应用初始化完成")
                except Exception as e3:
                    print(f"❌ 单页面应用失败: {e3}")

                    # 4. 尝试测试版本
                    try:
                        from test_app import app
                        print("✓ 测试版本初始化完成")
                    except Exception as e4:
                        print(f"❌ 所有版本都失败: {e4}")
                        print("\n详细错误信息:")
                        import traceback
                        traceback.print_exc()
                        return

        if app is None:
            print("❌ 无法启动任何版本的应用")
            return

        print("=" * 50)
        print("🚀 启动成功!")
        print("📊 访问地址: http://localhost:8050")
        print("📊 或访问: http://127.0.0.1:8050")
        print("=" * 50)
        print("按 Ctrl+C 停止服务")

        app.run(
            debug=True,
            host='127.0.0.1',
            port=8050,
            dev_tools_hot_reload=True
        )

    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
