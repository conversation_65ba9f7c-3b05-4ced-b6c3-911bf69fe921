"""
最小化测试版本 - 验证基本功能
"""

print("开始导入模块...")

try:
    import sys
    print("✓ sys模块导入成功")
    
    from pathlib import Path
    print("✓ pathlib模块导入成功")
    
    # 添加项目根目录到路径
    root_dir = Path(__file__).parent.parent
    sys.path.append(str(root_dir))
    print(f"✓ 添加路径: {root_dir}")
    
    import dash
    print(f"✓ Dash导入成功，版本: {dash.__version__}")
    
    from dash import dcc, html
    print("✓ Dash组件导入成功")
    
    import plotly.graph_objects as go
    print("✓ Plotly导入成功")
    
    import pandas as pd
    print("✓ Pandas导入成功")
    
    import numpy as np
    print("✓ NumPy导入成功")
    
    # 尝试导入数据服务
    try:
        from data_service import data_service
        print("✓ 数据服务导入成功")
    except Exception as e:
        print(f"⚠️ 数据服务导入失败: {e}")
    
    # 创建最简单的应用
    app = dash.Dash(__name__)
    
    app.layout = html.Div([
        html.H1("金融量化展示平台 - 测试版"),
        html.P("如果您看到这个页面，说明基本功能正常！"),
        html.Hr(),
        html.H3("功能测试:"),
        html.Ul([
            html.Li("✓ Dash框架正常"),
            html.Li("✓ 基础组件可用"),
            html.Li("✓ 样式渲染正常"),
        ]),
        html.Hr(),
        dcc.Graph(
            figure={
                'data': [
                    go.Scatter(
                        x=[1, 2, 3, 4],
                        y=[10, 11, 12, 13],
                        mode='lines+markers',
                        name='测试数据'
                    )
                ],
                'layout': {
                    'title': '测试图表',
                    'xaxis': {'title': 'X轴'},
                    'yaxis': {'title': 'Y轴'}
                }
            }
        )
    ])
    
    print("✓ 应用创建成功")
    print("=" * 50)
    print("🚀 启动测试应用...")
    print("📊 访问地址: http://127.0.0.1:8051")
    print("=" * 50)
    
    app.run(debug=True, host='127.0.0.1', port=8051)
    
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请检查是否安装了必要的依赖包")
except Exception as e:
    print(f"❌ 其他错误: {e}")
    import traceback
    traceback.print_exc()
