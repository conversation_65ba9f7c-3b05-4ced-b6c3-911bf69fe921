"""
实时行情页面 - 实时股票行情展示
"""

import dash
from dash import dcc, html, Input, Output, callback, dash_table
import dash_bootstrap_components as dbc
import plotly.graph_objects as go
import plotly.express as px
import pandas as pd
import numpy as np
from datetime import datetime
import sys
from pathlib import Path

# 添加项目根目录到路径
root_dir = Path(__file__).parent.parent.parent
sys.path.append(str(root_dir))

from data_service import data_service

# 页面布局
layout = html.Div([
    # 页面标题
    dbc.Row([
        dbc.Col([
            html.H2("📊 实时行情", className="mb-3"),
            html.P("实时股票价格和成交数据", className="text-muted")
        ])
    ], className="mb-4"),
    
    # 市场概览卡片
    dbc.Row([
        dbc.Col([
            dbc.Card([
                dbc.CardBody([
                    html.H4(id="market-total-stocks", className="text-primary"),
                    html.P("总股票数", className="text-muted mb-0")
                ])
            ])
        ], md=3),
        
        dbc.Col([
            dbc.Card([
                dbc.CardBody([
                    html.H4(id="market-up-stocks", className="text-danger"),
                    html.P("上涨", className="text-muted mb-0")
                ])
            ])
        ], md=3),
        
        dbc.Col([
            dbc.Card([
                dbc.CardBody([
                    html.H4(id="market-down-stocks", className="text-success"),
                    html.P("下跌", className="text-muted mb-0")
                ])
            ])
        ], md=3),
        
        dbc.Col([
            dbc.Card([
                dbc.CardBody([
                    html.H4(id="market-avg-change", className="text-info"),
                    html.P("平均涨跌幅", className="text-muted mb-0")
                ])
            ])
        ], md=3),
    ], className="mb-4"),
    
    # 控制面板和图表
    dbc.Row([
        dbc.Col([
            dbc.Card([
                dbc.CardHeader("筛选控制"),
                dbc.CardBody([
                    # 排序方式
                    html.Label("排序方式:", className="fw-bold mb-2"),
                    dcc.Dropdown(
                        id='market-sort-by',
                        options=[
                            {'label': '涨跌幅', 'value': 'change_pct'},
                            {'label': '涨跌额', 'value': 'change'},
                            {'label': '最新价', 'value': 'last_price'},
                            {'label': '成交量', 'value': 'volume'},
                            {'label': '成交额', 'value': 'amount'},
                        ],
                        value='change_pct',
                        className="mb-3"
                    ),
                    
                    # 排序方向
                    dbc.RadioItems(
                        id='market-sort-order',
                        options=[
                            {'label': '降序', 'value': 'desc'},
                            {'label': '升序', 'value': 'asc'},
                        ],
                        value='desc',
                        className="mb-3"
                    ),
                    
                    # 显示数量
                    html.Label("显示数量:", className="fw-bold mb-2"),
                    dcc.Slider(
                        id='market-display-count',
                        min=10,
                        max=100,
                        step=10,
                        value=50,
                        marks={i: str(i) for i in range(10, 101, 20)},
                        className="mb-3"
                    ),
                    
                    # 刷新按钮
                    dbc.Button(
                        "刷新数据",
                        id='market-refresh-btn',
                        color="primary",
                        className="w-100 mb-3"
                    ),
                    
                    # 自动刷新开关
                    dbc.Switch(
                        id="market-auto-refresh",
                        label="自动刷新",
                        value=True,
                    ),
                ])
            ])
        ], md=3),
        
        # 涨跌分布图
        dbc.Col([
            dbc.Card([
                dbc.CardHeader("涨跌分布"),
                dbc.CardBody([
                    dcc.Graph(id="market-distribution-chart")
                ])
            ])
        ], md=9)
    ], className="mb-4"),
    
    # 实时行情表格
    dbc.Row([
        dbc.Col([
            dbc.Card([
                dbc.CardHeader([
                    html.H5("实时行情数据", className="mb-0"),
                    html.Small(id="market-update-time", className="text-muted")
                ]),
                dbc.CardBody([
                    dcc.Loading(
                        id="market-table-loading",
                        children=[
                            html.Div(id="market-data-table")
                        ],
                        type="default"
                    )
                ])
            ])
        ])
    ])
])

# 回调函数
@callback(
    [Output('market-total-stocks', 'children'),
     Output('market-up-stocks', 'children'),
     Output('market-down-stocks', 'children'),
     Output('market-avg-change', 'children'),
     Output('market-distribution-chart', 'figure'),
     Output('market-data-table', 'children'),
     Output('market-update-time', 'children')],
    [Input('global-data-store', 'data'),
     Input('market-sort-by', 'value'),
     Input('market-sort-order', 'value'),
     Input('market-display-count', 'value'),
     Input('market-refresh-btn', 'n_clicks'),
     Input('market-auto-refresh', 'value')]
)
def update_market_page(global_data, sort_by, sort_order, display_count, refresh_clicks, auto_refresh):
    """更新实时行情页面"""
    
    if not global_data or not global_data.get('market_data'):
        return "0", "0", "0", "0%", {}, "暂无数据", "未更新"
    
    market_data = global_data['market_data']
    
    # 计算市场统计
    total_stocks = len(market_data)
    up_stocks = len([d for d in market_data if d.get('change', 0) > 0])
    down_stocks = len([d for d in market_data if d.get('change', 0) < 0])
    
    # 计算平均涨跌幅
    avg_change_pct = np.mean([d.get('change_pct', 0) for d in market_data])
    
    # 涨跌分布图
    change_pcts = [d.get('change_pct', 0) for d in market_data]
    
    fig = go.Figure()
    fig.add_trace(go.Histogram(
        x=change_pcts,
        nbinsx=20,
        name='涨跌幅分布',
        marker_color='lightblue',
        opacity=0.7
    ))
    
    fig.update_layout(
        title="涨跌幅分布图",
        xaxis_title="涨跌幅 (%)",
        yaxis_title="股票数量",
        height=300,
        margin=dict(l=20, r=20, t=40, b=20)
    )
    
    # 排序数据
    reverse = (sort_order == 'desc')
    sorted_data = sorted(market_data, key=lambda x: x.get(sort_by, 0), reverse=reverse)
    display_data = sorted_data[:display_count]
    
    # 创建数据表格
    df = pd.DataFrame(display_data)
    if not df.empty:
        # 格式化数据
        df['change_pct_formatted'] = df['change_pct'].apply(lambda x: f"{x:.2%}")
        df['last_price_formatted'] = df['last_price'].apply(lambda x: f"{x:.2f}")
        df['change_formatted'] = df['change'].apply(lambda x: f"{x:+.2f}")
        df['volume_formatted'] = df['volume'].apply(lambda x: f"{x:,.0f}")
        df['amount_formatted'] = df['amount'].apply(lambda x: f"{x:,.0f}")
        
        table = dash_table.DataTable(
            data=df.to_dict('records'),
            columns=[
                {'name': '股票代码', 'id': 'symbol'},
                {'name': '股票名称', 'id': 'name'},
                {'name': '最新价', 'id': 'last_price_formatted'},
                {'name': '涨跌额', 'id': 'change_formatted'},
                {'name': '涨跌幅', 'id': 'change_pct_formatted'},
                {'name': '成交量', 'id': 'volume_formatted'},
                {'name': '成交额', 'id': 'amount_formatted'},
                {'name': '更新时间', 'id': 'datetime'},
            ],
            sort_action="native",
            filter_action="native",
            page_action="native",
            page_current=0,
            page_size=20,
            style_cell={
                'textAlign': 'center',
                'fontSize': '12px',
                'padding': '8px'
            },
            style_header={
                'backgroundColor': 'rgb(230, 230, 230)',
                'fontWeight': 'bold'
            },
            style_data_conditional=[
                {
                    'if': {
                        'filter_query': '{change} > 0',
                        'column_id': ['change_formatted', 'change_pct_formatted']
                    },
                    'color': 'red',
                },
                {
                    'if': {
                        'filter_query': '{change} < 0',
                        'column_id': ['change_formatted', 'change_pct_formatted']
                    },
                    'color': 'green',
                }
            ]
        )
    else:
        table = "暂无数据"
    
    # 更新时间
    update_time = f"最后更新: {datetime.now().strftime('%H:%M:%S')}"
    
    return (
        str(total_stocks),
        str(up_stocks),
        str(down_stocks),
        f"{avg_change_pct:.2%}",
        fig,
        table,
        update_time
    )
