"""
市场分析页面 - 整体市场分析和板块分析
"""

import dash
from dash import dcc, html, Input, Output, callback
import dash_bootstrap_components as dbc
import plotly.graph_objects as go
import plotly.express as px
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
from pathlib import Path

# 添加项目根目录到路径
root_dir = Path(__file__).parent.parent.parent
sys.path.append(str(root_dir))

from data_service import data_service

# 页面布局
layout = html.Div([
    # 页面标题
    dbc.Row([
        dbc.Col([
            html.H2("📊 市场分析", className="mb-3"),
            html.P("整体市场趋势和板块分析", className="text-muted")
        ])
    ], className="mb-4"),
    
    # 市场概览指标
    dbc.Row([
        dbc.Col([
            dbc.Card([
                dbc.CardBody([
                    html.H4(id="analysis-market-index", className="text-primary"),
                    html.P("市场指数", className="text-muted mb-0")
                ])
            ])
        ], md=2),
        
        dbc.Col([
            dbc.Card([
                dbc.CardBody([
                    html.H4(id="analysis-up-down-ratio", className="text-info"),
                    html.P("涨跌比", className="text-muted mb-0")
                ])
            ])
        ], md=2),
        
        dbc.Col([
            dbc.Card([
                dbc.CardBody([
                    html.H4(id="analysis-avg-change", className="text-warning"),
                    html.P("平均涨幅", className="text-muted mb-0")
                ])
            ])
        ], md=2),
        
        dbc.Col([
            dbc.Card([
                dbc.CardBody([
                    html.H4(id="analysis-total-volume", className="text-success"),
                    html.P("总成交量", className="text-muted mb-0")
                ])
            ])
        ], md=3),
        
        dbc.Col([
            dbc.Card([
                dbc.CardBody([
                    html.H4(id="analysis-total-amount", className="text-danger"),
                    html.P("总成交额", className="text-muted mb-0")
                ])
            ])
        ], md=3),
    ], className="mb-4"),
    
    # 图表区域
    dbc.Row([
        # 涨跌分布
        dbc.Col([
            dbc.Card([
                dbc.CardHeader("涨跌分布分析"),
                dbc.CardBody([
                    dcc.Graph(id="analysis-distribution-chart")
                ])
            ])
        ], md=6),
        
        # 成交量分析
        dbc.Col([
            dbc.Card([
                dbc.CardHeader("成交量分析"),
                dbc.CardBody([
                    dcc.Graph(id="analysis-volume-chart")
                ])
            ])
        ], md=6),
    ], className="mb-4"),
    
    # 板块分析
    dbc.Row([
        dbc.Col([
            dbc.Card([
                dbc.CardHeader("板块表现分析"),
                dbc.CardBody([
                    dcc.Graph(id="analysis-sector-chart")
                ])
            ])
        ], md=8),
        
        dbc.Col([
            dbc.Card([
                dbc.CardHeader("市场热点"),
                dbc.CardBody([
                    html.Div(id="analysis-hot-sectors")
                ])
            ])
        ], md=4),
    ], className="mb-4"),
    
    # 市场情绪分析
    dbc.Row([
        dbc.Col([
            dbc.Card([
                dbc.CardHeader("市场情绪指标"),
                dbc.CardBody([
                    dbc.Row([
                        dbc.Col([
                            html.H5("恐慌贪婪指数"),
                            html.Div(id="fear-greed-gauge")
                        ], md=6),
                        
                        dbc.Col([
                            html.H5("市场强度"),
                            html.Div(id="market-strength")
                        ], md=6),
                    ])
                ])
            ])
        ])
    ])
])

# 回调函数
@callback(
    [Output('analysis-market-index', 'children'),
     Output('analysis-up-down-ratio', 'children'),
     Output('analysis-avg-change', 'children'),
     Output('analysis-total-volume', 'children'),
     Output('analysis-total-amount', 'children'),
     Output('analysis-distribution-chart', 'figure'),
     Output('analysis-volume-chart', 'figure'),
     Output('analysis-sector-chart', 'figure'),
     Output('analysis-hot-sectors', 'children'),
     Output('fear-greed-gauge', 'children'),
     Output('market-strength', 'children')],
    Input('global-data-store', 'data')
)
def update_market_analysis(global_data):
    """更新市场分析页面"""
    
    if not global_data or not global_data.get('market_data'):
        empty_values = ["--"] * 5 + [{}] * 3 + ["暂无数据"] * 3
        return empty_values
    
    market_data = global_data['market_data']
    
    # 计算市场指标
    total_stocks = len(market_data)
    up_stocks = len([d for d in market_data if d.get('change', 0) > 0])
    down_stocks = len([d for d in market_data if d.get('change', 0) < 0])
    
    # 市场指数 (模拟)
    market_index = f"{3000 + np.random.randint(-100, 100):.0f}"
    
    # 涨跌比
    up_down_ratio = f"{up_stocks}:{down_stocks}"
    
    # 平均涨幅
    avg_change = np.mean([d.get('change_pct', 0) for d in market_data])
    avg_change_str = f"{avg_change:.2%}"
    
    # 总成交量和成交额
    total_volume = sum([d.get('volume', 0) for d in market_data])
    total_amount = sum([d.get('amount', 0) for d in market_data])
    
    volume_str = f"{total_volume/1e8:.1f}亿"
    amount_str = f"{total_amount/1e8:.0f}亿"
    
    # 涨跌分布图
    change_pcts = [d.get('change_pct', 0) * 100 for d in market_data]
    
    distribution_fig = go.Figure()
    distribution_fig.add_trace(go.Histogram(
        x=change_pcts,
        nbinsx=30,
        name='涨跌分布',
        marker_color='lightblue',
        opacity=0.7
    ))
    distribution_fig.update_layout(
        title="股票涨跌幅分布",
        xaxis_title="涨跌幅 (%)",
        yaxis_title="股票数量",
        height=300
    )
    
    # 成交量分析图
    volumes = [d.get('volume', 0) for d in market_data]
    volume_ranges = ['<1万', '1-10万', '10-100万', '100万-1000万', '>1000万']
    volume_counts = [
        len([v for v in volumes if v < 10000]),
        len([v for v in volumes if 10000 <= v < 100000]),
        len([v for v in volumes if 100000 <= v < 1000000]),
        len([v for v in volumes if 1000000 <= v < 10000000]),
        len([v for v in volumes if v >= 10000000])
    ]
    
    volume_fig = go.Figure()
    volume_fig.add_trace(go.Bar(
        x=volume_ranges,
        y=volume_counts,
        marker_color=['#ff9999', '#66b3ff', '#99ff99', '#ffcc99', '#ff99cc']
    ))
    volume_fig.update_layout(
        title="成交量分布",
        xaxis_title="成交量区间",
        yaxis_title="股票数量",
        height=300
    )
    
    # 板块分析图 (模拟数据)
    sectors = ['银行', '地产', '科技', '医药', '消费', '能源', '制造', '金融']
    sector_changes = np.random.normal(0, 2, len(sectors))
    
    colors = ['red' if x > 0 else 'green' for x in sector_changes]
    
    sector_fig = go.Figure()
    sector_fig.add_trace(go.Bar(
        x=sectors,
        y=sector_changes,
        marker_color=colors,
        opacity=0.7
    ))
    sector_fig.update_layout(
        title="板块涨跌幅排行",
        xaxis_title="板块",
        yaxis_title="平均涨跌幅 (%)",
        height=300
    )
    
    # 热点板块
    sorted_sectors = sorted(zip(sectors, sector_changes), key=lambda x: x[1], reverse=True)
    hot_sectors = []
    for i, (sector, change) in enumerate(sorted_sectors[:5]):
        color = "text-danger" if change > 0 else "text-success"
        hot_sectors.append(
            html.Div([
                html.Span(f"{i+1}. ", className="text-muted"),
                html.Span(sector, className="fw-bold"),
                html.Span(f" {change:+.2f}%", className=color)
            ], className="mb-2")
        )
    
    # 恐慌贪婪指数 (模拟)
    fear_greed_value = np.random.randint(20, 80)
    if fear_greed_value < 25:
        fg_color = "danger"
        fg_text = "极度恐慌"
    elif fear_greed_value < 45:
        fg_color = "warning"
        fg_text = "恐慌"
    elif fear_greed_value < 55:
        fg_color = "secondary"
        fg_text = "中性"
    elif fear_greed_value < 75:
        fg_color = "info"
        fg_text = "贪婪"
    else:
        fg_color = "success"
        fg_text = "极度贪婪"
    
    fear_greed_gauge = dbc.Progress(
        value=fear_greed_value,
        color=fg_color,
        className="mb-2",
        style={"height": "20px"}
    )
    fear_greed_display = html.Div([
        fear_greed_gauge,
        html.P(f"{fear_greed_value} - {fg_text}", className="text-center mb-0")
    ])
    
    # 市场强度
    market_strength_value = (up_stocks / total_stocks) * 100 if total_stocks > 0 else 0
    if market_strength_value > 60:
        ms_color = "success"
        ms_text = "强势"
    elif market_strength_value > 40:
        ms_color = "warning"
        ms_text = "震荡"
    else:
        ms_color = "danger"
        ms_text = "弱势"
    
    market_strength_gauge = dbc.Progress(
        value=market_strength_value,
        color=ms_color,
        className="mb-2",
        style={"height": "20px"}
    )
    market_strength_display = html.Div([
        market_strength_gauge,
        html.P(f"{market_strength_value:.1f}% - {ms_text}", className="text-center mb-0")
    ])
    
    return (
        market_index,
        up_down_ratio,
        avg_change_str,
        volume_str,
        amount_str,
        distribution_fig,
        volume_fig,
        sector_fig,
        hot_sectors,
        fear_greed_display,
        market_strength_display
    )
