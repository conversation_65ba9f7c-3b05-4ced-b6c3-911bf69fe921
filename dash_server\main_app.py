"""
多页面金融量化展示平台 - 主应用文件
"""

import dash
from dash import dcc, html, Input, Output, callback
import sys
from pathlib import Path

# 尝试导入Bootstrap组件，如果失败则使用基础HTML组件
try:
    import dash_bootstrap_components as dbc
    HAS_BOOTSTRAP = True
except ImportError:
    print("⚠️ dash-bootstrap-components未安装，使用基础组件")
    HAS_BOOTSTRAP = False
    # 创建简单的替代组件
    class dbc:
        @staticmethod
        def NavbarSimple(**kwargs):
            children = kwargs.get('children', [])
            brand = kwargs.get('brand', '')
            return html.Nav([
                html.Div([
                    html.H3(brand, style={'color': 'white', 'margin': '0'}),
                    html.Div(children, style={'display': 'flex', 'gap': '20px'})
                ], style={'display': 'flex', 'justify-content': 'space-between', 'align-items': 'center', 'padding': '10px 20px'})
            ], style={'background-color': '#007bff', 'margin-bottom': '20px'})

        @staticmethod
        def NavItem(children):
            return html.Div(children, style={'display': 'inline-block'})

        @staticmethod
        def NavLink(children, **kwargs):
            href = kwargs.get('href', '#')
            return html.A(children, href=href, style={'color': 'white', 'text-decoration': 'none', 'padding': '5px 10px'})

        @staticmethod
        def Card(children, **kwargs):
            return html.Div(children, style={'border': '1px solid #ddd', 'border-radius': '5px', 'margin': '10px 0'})

        @staticmethod
        def CardHeader(children):
            return html.Div(children, style={'background-color': '#f8f9fa', 'padding': '10px', 'border-bottom': '1px solid #ddd', 'font-weight': 'bold'})

        @staticmethod
        def CardBody(children):
            return html.Div(children, style={'padding': '15px'})

        @staticmethod
        def Button(children, **kwargs):
            return html.Button(children, **kwargs, style={'padding': '8px 16px', 'border': 'none', 'border-radius': '4px', 'background-color': '#007bff', 'color': 'white', 'cursor': 'pointer'})

        @staticmethod
        def Jumbotron(children, **kwargs):
            return html.Div(children, style={'background-color': '#e9ecef', 'padding': '40px', 'border-radius': '10px', 'margin': '20px 0'})

        class themes:
            BOOTSTRAP = None

        class icons:
            FONT_AWESOME = None

# 添加项目根目录到路径
root_dir = Path(__file__).parent.parent
sys.path.append(str(root_dir))

# 初始化Dash应用
if HAS_BOOTSTRAP:
    app = dash.Dash(
        __name__,
        external_stylesheets=[dbc.themes.BOOTSTRAP, dbc.icons.FONT_AWESOME],
        suppress_callback_exceptions=True,
        meta_tags=[
            {"name": "viewport", "content": "width=device-width, initial-scale=1"}
        ]
    )
else:
    app = dash.Dash(
        __name__,
        suppress_callback_exceptions=True,
        meta_tags=[
            {"name": "viewport", "content": "width=device-width, initial-scale=1"}
        ]
    )

app.title = "金融量化展示平台"

# 导航栏组件
def create_navbar():
    """创建导航栏"""
    return dbc.NavbarSimple(
        children=[
            dbc.NavItem(dbc.NavLink("首页", href="/", active="exact")),
            dbc.NavItem(dbc.NavLink("K线分析", href="/kline", active="exact")),
            dbc.NavItem(dbc.NavLink("实时行情", href="/market", active="exact")),
            dbc.NavItem(dbc.NavLink("技术指标", href="/indicators", active="exact")),
            dbc.NavItem(dbc.NavLink("市场分析", href="/analysis", active="exact")),
            dbc.NavItem(dbc.NavLink("数据管理", href="/data", active="exact")),
        ],
        brand="📊 金融量化平台",
        brand_href="/",
        color="primary",
        dark=True,
        fluid=True,
        className="mb-3"
    )

# 主布局
app.layout = html.Div([
    dcc.Location(id='url', refresh=False),
    create_navbar(),
    html.Div(id='page-content', className="container-fluid"),
    
    # 全局存储
    dcc.Store(id='global-data-store'),
    dcc.Store(id='selected-stock-store', data='000001.SZ'),
    
    # 全局定时器
    dcc.Interval(
        id='global-interval',
        interval=30*1000,  # 30秒
        n_intervals=0
    )
])

# 页面路由回调
@app.callback(
    Output('page-content', 'children'),
    Input('url', 'pathname')
)
def display_page(pathname):
    """根据URL显示对应页面"""
    if pathname == '/':
        from pages import home
        return home.layout
    elif pathname == '/kline':
        from pages import kline_analysis
        return kline_analysis.layout
    elif pathname == '/market':
        from pages import real_time_market
        return real_time_market.layout
    elif pathname == '/indicators':
        from pages import technical_indicators
        return technical_indicators.layout
    elif pathname == '/analysis':
        from pages import market_analysis
        return market_analysis.layout
    elif pathname == '/data':
        from pages import data_management
        return data_management.layout
    else:
        return html.Div([
            html.H1("404 - 页面未找到", className="text-center mt-5"),
            html.P("请检查URL是否正确", className="text-center"),
            dbc.Button("返回首页", href="/", color="primary", className="d-block mx-auto")
        ])

# 全局数据更新回调
@app.callback(
    Output('global-data-store', 'data'),
    [Input('global-interval', 'n_intervals'),
     Input('selected-stock-store', 'data')]
)
def update_global_data(n_intervals, selected_stock):
    """更新全局数据"""
    from data_service import data_service
    
    try:
        # 获取实时行情数据
        market_data = data_service.get_tick_data(limit=100)
        
        # 获取选中股票的详细信息
        stock_detail = data_service.get_stock_detail(selected_stock) if selected_stock else None
        
        return {
            'market_data': market_data,
            'stock_detail': stock_detail,
            'selected_stock': selected_stock,
            'last_update': n_intervals
        }
    except Exception as e:
        print(f"全局数据更新失败: {e}")
        return {
            'market_data': [],
            'stock_detail': None,
            'selected_stock': selected_stock,
            'last_update': n_intervals,
            'error': str(e)
        }

if __name__ == '__main__':
    app.run(debug=True, host='127.0.0.1', port=8050)
